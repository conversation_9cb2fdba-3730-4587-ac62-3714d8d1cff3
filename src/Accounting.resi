module Actions = Accounting__Actions
module Computer = Accounting__Computer
module Formatter = Accounting__Formatter
module Maker = Accounting__Maker
module Reducer = Accounting__Reducer
module Serializer = Accounting__Serializer

@genType.as("Cart") type cart = Accounting__Types.cart
@genType.as("CartProduct") type product = Accounting__Types.product
@genType.as("CartDiscount") type discount = Accounting__Types.discount
@genType.as("CartProductFee") type fee = Accounting__Types.fee
@genType.as("Tax") type tax = Accounting__Types.tax

@genType let compute: Accounting__Types.cart => Accounting__Types.cart

@genType let serialize: Accounting__Types.cart => string
@genType let deserialize: string => Accounting__Types.cart

@genType let format: Accounting__Types.cart => Accounting__Types.cart
@genType
let formatAmount: (
  {"currency": Accounting__Types.currency, "bulkUnit": option<string>, "precision": option<int>},
  float,
) => string
@genType
let formatAmountFromString: (
  {"currency": Accounting__Types.currency, "bulkUnit": option<string>, "precision": option<int>},
  string,
) => string
@genType
let formatAmountFromBig: (
  {"currency": Accounting__Types.currency, "bulkUnit": option<string>, "precision": option<int>},
  Big.t,
) => string

@genType let make: Accounting__Types.cartInput => Accounting__Types.cart

@genType let fromRawProductQuantity: (~capacityPrecision: int=?, int) => Big.t
@genType let toRawProductQuantity: (~capacityPrecision: int=?, Big.t) => int
@genType let isBulk: Accounting__Types.product => bool
