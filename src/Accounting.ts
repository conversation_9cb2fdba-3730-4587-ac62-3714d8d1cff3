import type { t as Big_t } from './Big.gen'
import Big from 'big.js'

import type {
  cartInput,
  cart as GenCart,
  product as GenProduct,
  productStruct as GenProductStruct,
} from './Accounting__Types.gen'

import {
  make,
  compute as genCompute,
  serialize as genSerialize,
  deserialize as genDeserialize,
  fromRawProductQuantity,
  toRawProductQuantity,
} from './Accounting.gen'

type LegacyGenProduct =
  | { tag: 'Unit'; value: GenProductStruct<number> }
  | { tag: 'Bulk'; value: [GenProductStruct<Big_t>, number] }

// ReScript legacy (v10) vs modern (v11) TS generated variant format:
type CompatibleProductVariant = LegacyGenProduct | GenProduct

function isLegacyProductVariant(
  variant: CompatibleProductVariant,
): variant is LegacyGenProduct {
  return 'tag' in variant && 'value' in variant
}

type UnitProduct = GenProductStruct<number> & {
  capacityPrecision: null
  bulk: false
}
type BulkProduct = GenProductStruct<Big_t> & {
  capacityPrecision: number
  bulk: true
}

export type Product = UnitProduct | BulkProduct
export interface Cart extends Omit<GenCart, 'products'> {
  products: Product[]
}

export function _productFromGeneratedStructure(
  product: CompatibleProductVariant,
): Product {
  if (isLegacyProductVariant(product)) {
    switch (product.tag) {
      case 'Unit':
        return {
          ...product.value,
          capacityPrecision: null,
          bulk: false,
        }
      case 'Bulk':
        return {
          ...product.value[0],
          capacityPrecision: product.value[1],
          bulk: true,
        }
      default:
        throw Error(
          `Product malformed (legacy variant): (${(product as any).tag})`,
        )
    }
  } else {
    switch (product.TAG) {
      case 'Unit':
        return {
          ...product._0,
          capacityPrecision: null,
          bulk: false,
        }
      case 'Bulk':
        return {
          ...product._0,
          capacityPrecision: product._1,
          bulk: true,
        }
      default:
        throw Error(`Product malformed: (${(product as any).TAG})`)
    }
  }
}

export function _productToGeneratedStructure(product: Product): GenProduct {
  if (product.capacityPrecision) {
    const { capacityPrecision, ...rest } = product
    return {
      TAG: 'Bulk',
      _0: rest,
      _1: capacityPrecision,
    }
  }
  return {
    TAG: 'Unit',
    _0: product as GenProductStruct<number>,
  }
}

export function _make(cart: cartInput): Cart {
  const newCart = make(cart)

  return {
    ...newCart,
    products: newCart.products.map((product) =>
      _productFromGeneratedStructure(product),
    ),
  }
}

export function _compute(cart: Cart): Cart {
  const computedCart = genCompute({
    ...cart,
    products: cart.products.map((product) =>
      _productToGeneratedStructure(product),
    ),
  })

  return {
    ...computedCart,
    products: computedCart.products.map((product) =>
      _productFromGeneratedStructure(product),
    ),
  }
}

export function _serialize(cart: Cart): string {
  const cartOriginal = {
    ...cart,
    products: cart.products.map((product) =>
      _productToGeneratedStructure(product),
    ),
  }

  return genSerialize(cartOriginal)
}

export function _deserialize(cartStringified: string): Cart {
  const genCart = genDeserialize(cartStringified)

  return {
    ...genCart,
    products: genCart.products.map((product) =>
      _productFromGeneratedStructure(product),
    ),
  }
}

export function _isBulkProduct(product: Product): product is BulkProduct {
  return (
    product.capacityPrecision !== null &&
    product.capacityPrecision !== undefined
  )
}

export function _isUnitProduct(product: Product): product is UnitProduct {
  return !_isBulkProduct(product)
}

export function _cartIntegerToFloat(
  value: Big_t | number,
  precision?: number | null,
): number {
  return fromRawProductQuantity(
    precision ?? undefined,
    new Big(value).toNumber(),
  ).toNumber()
}

export function _cartFloatToInteger(
  value: Big_t | number,
  precision?: number | null,
): number {
  return toRawProductQuantity(precision ?? undefined, new Big(value))
}
