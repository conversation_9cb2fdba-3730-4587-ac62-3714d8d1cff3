open Accounting__Types
open Accounting__Exception

// Generates an UUID
@module("uuid")
external makeUUID: unit => string = "v4"

// Get all supported fee kinds
let getAllFeeKinds = () => [Transport, Taxes, Other]

// Given a product and a feeKind
// check if the feeKind can be added to the product.
// Remember we cannot have more than one
// feeKind in the same product
let canAcceptFeeKind = (product: product, ~feeKind: feeKind) =>
  switch product {
  | Unit({fees})
  | Bulk({fees}, _) =>
    fees->Array.keep(fee => feeKind === fee.kind)->Array.length === 0
  }

// Get all fee kinds that can be added to a certain product
let getAvailableFeeKinds = (product: product): array<feeKind> =>
  getAllFeeKinds()->Array.keep(feeKind => product->canAcceptFeeKind(~feeKind))

// Checks if the product has available fee kinds
let hasAvailableFeeKinds = (product: product): bool =>
  switch product {
  | Unit({availablesFeeKinds}) | Bulk({availablesFeeKinds}, _) =>
    availablesFeeKinds->Option.getExn->Array.length > 0
  }

// Given a product and a string
// checks if the product id or stockKeepingUnit equals to the string
let productIsIdentifiedBy = (~key: option<string>, product: product) =>
  switch (product, key) {
  | (
      Unit({id, identifier, stockKeepingUnit}) | Bulk({id, identifier, stockKeepingUnit}, _),
      Some(key),
    ) =>
    id === key ||
      switch (identifier, stockKeepingUnit) {
      | (Some(identifier), Some(stockKeepingUnit)) => identifier === key || stockKeepingUnit === key
      | (Some(identifier), None) => identifier === key
      | (None, Some(stockKeepingUnit)) => stockKeepingUnit === key
      | (None, None) => false
      }
  | _ => false
  }

// Checks if a product identified by the key provided
// is already in the cart
let cartHasProduct = (cart: cart, key: option<string>) =>
  switch cart.products->Array.getBy(productIsIdentifiedBy(~key, ...)) {
  | Some(_foundProduct) => true
  | None => false
  }

// Gets a product by its key from an array of products
let getProductByKey = (products: array<product>, key) =>
  switch products->Array.getBy(productIsIdentifiedBy(~key, ...)) {
  | Some(product) => product
  | None => raise(NotFound("Product not found"))
  }

// Returns if a Big number is a multiple of another
let isMultipleOf = (a: Big.t, b: Big.t) => mod_float(a->Big.toFloat, b->Big.toFloat) === 0.

// Converts a big value to float
let bigToFloat = big => big->Big.toFloat

let fromRawProductQuantity = (~capacityPrecision: option<int>=?, rawValue: int) =>
  switch capacityPrecision {
  | Some(precision) =>
    (rawValue->Float.fromInt /. Js.Math.pow_float(~base=10., ~exp=precision->Float.fromInt))
      ->Big.fromFloat
  | _ => rawValue->Float.fromInt->Big.fromFloat
  }

let toRawProductQuantity = (~capacityPrecision: option<int>=?, value: Big.t) =>
  switch capacityPrecision {
  | Some(precision) =>
    (value->Big.toFloat *. Js.Math.pow_float(~base=10., ~exp=precision->Float.fromInt))->Float.toInt
  | _ => value->Big.toFloat->Float.toInt
  }

let isBulk = (product: product) =>
  switch product {
  | Bulk(_, _) => true
  | Unit(_) => false
  }
