open Accounting__Types

module Utils = Accounting__Utils

// Shallow equal big js
let shallowEqualBig = (a, b) =>
  switch (a, b) {
  | (Some(a), Some(b)) => Big.eq(a, b)
  | _ => false
  }

// Shallow equal basic object
let shallowEqualObject = %raw(`(a, b) => {
  if (Array.isArray(a) && Array.isArray(b) && a.length === 0 && b.length === 0) {
    return true
  }
  return JSON.stringify(a) === JSON.stringify(b)
}`)

// // Shallow equal product
// let shallowEqualProduct = (oldProduct, newProduct) =>
//   oldProduct.identifier === newProduct.identifier &&
//   oldProduct.stockKeepingUnit === newProduct.stockKeepingUnit &&
//   oldProduct.name === newProduct.name &&
//   oldProduct.description === newProduct.description &&
//   oldProduct.unitPrice === newProduct.unitPrice &&
//   Big.eq(oldProduct.stock, newProduct.stock) &&
//   shallowEqualObject(oldProduct.bulk, newProduct.bulk) &&
//   Big.eq(oldProduct.expectedQuantity, newProduct.expectedQuantity) &&
//   shallowEqualObject(oldProduct.expectedQuantityWarning, newProduct.expectedQuantityWarning) &&
//   shallowEqualBig(oldProduct.packaging, newProduct.packaging) &&
//   Big.eq(oldProduct.quantity, newProduct.quantity) &&
//   shallowEqualObject(oldProduct.fees, newProduct.fees) &&
//   shallowEqualObject(oldProduct.discounts, newProduct.discounts) &&
//   oldProduct.id === newProduct.id &&
//   shallowEqualObject(oldProduct.availablesFeeKinds, newProduct.availablesFeeKinds) &&
//   shallowEqualBig(oldProduct.totalPrice, newProduct.totalPrice) &&
//   shallowEqualBig(oldProduct.totalLocalDiscounts, newProduct.totalLocalDiscounts) &&
//   shallowEqualBig(oldProduct.unitFee, newProduct.unitFee) &&
//   shallowEqualBig(oldProduct.totalFees, newProduct.totalFees) &&
//   shallowEqualBig(
//     oldProduct.totalAmountExcludingGlobalDiscounts,
//     newProduct.totalAmountExcludingGlobalDiscounts,
//   ) &&
//   shallowEqualBig(oldProduct.totalGlobalDiscounts, newProduct.totalGlobalDiscounts) &&
//   shallowEqualBig(oldProduct.totalDiscounts, newProduct.totalDiscounts) &&
//   shallowEqualObject(oldProduct.taxes, newProduct.taxes) &&
//   shallowEqualBig(oldProduct.totalAmountExcludingTaxes, newProduct.totalAmountExcludingTaxes) &&
//   shallowEqualBig(oldProduct.totalAmountOfTaxes, newProduct.totalAmountOfTaxes) &&
//   shallowEqualBig(oldProduct.totalAmountIncludingTaxes, newProduct.totalAmountIncludingTaxes) &&
//   shallowEqualBig(oldProduct.unitCost, newProduct.unitCost) &&
//   oldProduct.formattedStock === newProduct.formattedStock &&
//   oldProduct.formattedQuantity === newProduct.formattedQuantity &&
//   oldProduct.formattedExpectedQuantity === newProduct.formattedExpectedQuantity &&
//   oldProduct.formattedUnitPrice === newProduct.formattedUnitPrice &&
//   oldProduct.formattedTotalPrice === newProduct.formattedTotalPrice &&
//   oldProduct.formattedUnitFee === newProduct.formattedUnitFee &&
//   oldProduct.formattedTotalFees === newProduct.formattedTotalFees &&
//   oldProduct.formattedTotalLocalDiscounts === newProduct.formattedTotalLocalDiscounts &&
//   oldProduct.formattedTotalDiscounts === newProduct.formattedTotalDiscounts &&
//   oldProduct.formattedTotalAmountExcludingGlobalDiscounts ===
//     newProduct.formattedTotalAmountExcludingGlobalDiscounts &&
//   oldProduct.formattedTotalAmountExcludingTaxes === newProduct.formattedTotalAmountExcludingTaxes &&
//   oldProduct.formattedTotalAmountIncludingTaxes === newProduct.formattedTotalAmountIncludingTaxes

let make = (oldCart, newCart) => {
  let products = newCart.products->Array.map(product => {
    let productId = switch product {
    | Unit({id}) | Bulk({id}, _) => id
    }
    let oldProduct =
      oldCart.products->Array.getBy(Utils.productIsIdentifiedBy(~key=Some(productId), ...))

    switch oldProduct {
    | Some(oldProduct) if oldProduct == product => oldProduct
    | _ => product
    }
  })

  {...newCart, products}
}
