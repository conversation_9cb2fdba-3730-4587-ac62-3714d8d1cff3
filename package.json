{"name": "@wino/accounting", "version": "14.2.3", "description": "All the utilities necessary for managing the accounting", "author": "Léo Le <PERSON> <<EMAIL>>", "main": "src/Accounting.res", "scripts": {"bundle": "node scripts/bundle.js", "preversion": "yarn test", "postversion": "yarn bundle && scripts/publish.sh", "prepublishOnly": "yarn test && yarn build", "pub:xp": "yarn preversion && yarn postversion", "pub:stable": "yarn version", "build": "rescript build && tsc --p tsconfig-build.json --declaration", "dev:res": "rescript build -w", "test": "yarn build && jest --runInBand --config jest.config.res.js --coverage && jest --runInBand --config jest.config.ts.js", "dev:test": "yarn build && jest --runInBand --config jest.config.res.js --watchAll", "dev:ts:test": "jest --runInBand --config jest.config.ts.js --watchAll", "clean": "rescript clean -with-deps"}, "publishConfig": {"registry": "https://registry.npmjs.org"}, "repository": {"type": "git", "url": "git+https://github.com/winoteam/accounting.git"}, "bugs": {"url": "https://github.com/winoteam/accounting/issues"}, "homepage": "https://github.com/winoteam/accounting#readme", "keywords": ["accounting", "compute", "rescript"], "devDependencies": {"@babel/core": "7.15.8", "@babel/preset-env": "7.15.8", "@glennsl/rescript-jest": "0.11.0", "@types/jest": "27.4.0", "@types/node": "13.9.1", "babel-jest": "27.3.1", "child-process-promise": "2.2.1", "fs-extra": "9.0.1", "jest": "27.3.1", "rescript": "11.1.4", "ts-jest": "27.1.5", "typescript": "5.6.2"}, "dependencies": {"@types/big.js": "6.2.2", "big.js": "6.2.2", "uuid": "8.3.2"}}