open Jest

module Formatter = Accounting__Formatter

describe("Formatter", () => {
  open Expect
  open Formatter

  describe("postfixToString", () => {
    test(
      "should correctly convert euro currency postfix to string",
      () => expect(Currency(Eur)->postfixToString)->toBe(`€`),
    )

    test(
      "should correctly convert usd currency postfix to string",
      () => expect(Currency(Usd)->postfixToString)->toBe(`$`),
    )

    test(
      "should correctly convert pound currency postfix to string",
      () => expect(Currency(Pound)->postfixToString)->toBe(`£`),
    )

    test(
      "should correctly convert percent postfix to string",
      () => expect(Percent->postfixToString)->toBe("%"),
    )
  })

  describe("formatAmount", () => {
    test(
      "should correctly format amount with eur currency and precision",
      () => expect(12.9->formatAmount(~precision=2, ~currency=Eur))->toBe(`12.90 €`),
    )

    test(
      "should correctly format amount with usd currency and precision",
      () => expect(12.9->formatAmount(~currency=Usd, ~precision=3))->toBe(`$12.900`),
    )

    test(
      "should correctly format amount with eur currency, precision and bulk unit",
      () =>
        expect(
          12.9->formatAmount(~currency=Eur, ~precision=2, ~bulkUnit="kg"),
        )->toBe(`12.90 €/kg`),
    )

    test(
      "should correctly format amount with usd currency with precision and bulk unit",
      () =>
        expect(0.9->formatAmount(~currency=Usd, ~precision=2, ~bulkUnit="kg"))->toBe(`$0.90/kg`),
    )
  })

  describe("formatQuantity", () => {
    test(
      "should correctly format Big amount with bulk unit",
      () => expect(formatQuantity(1000., ~bulkUnit="kg"))->toBe("1000 kg"),
    )

    test(
      "should correctly format Big amount with bulk unit and precision",
      () => expect(formatQuantity(1., ~precision=3, ~bulkUnit="kg"))->toBe("1.000 kg"),
    )

    test(
      "should correctly format Big amount with precision only",
      () => expect(formatQuantity(10., ~precision=2))->toBe("10.00"),
    )

    test("should correctly format Big amount", () => expect(formatQuantity(10.))->toBe("10"))
  })
})
