import Big from 'big.js'
import {
  formatAmount,
  formatAmountFromBig,
  formatAmountFromString,
} from '../src/Accounting.gen'

describe('formatAmount', () => {
  test('accept Big', () => {
    expect(formatAmountFromBig({ currency: 'Eur' }, new Big(12))).toStrictEqual(
      '12 €',
    )
  })

  test('accept number', () => {
    expect(formatAmount({ currency: 'Eur' }, 12)).toStrictEqual('12 €')
  })

  test('accept string', () => {
    expect(formatAmountFromString({ currency: 'Eur' }, '12')).toStrictEqual(
      '12 €',
    )
  })

  test('with bulk unit', () => {
    expect(
      formatAmountFromString({ currency: 'Eur', bulkUnit: 'kg' }, '12'),
    ).toStrictEqual('12 €/kg')
  })
})
