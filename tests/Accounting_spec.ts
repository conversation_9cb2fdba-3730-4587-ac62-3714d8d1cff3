import {
  productinputs__quantity_0_unit_price_20_1281_tax_20,
  productinputs__bulk_gram_3_quantity_25_unit_price_5_69_tax_20,
} from './__fixtures__/Fixtures__ProductInputs.gen'

import { make as genMake } from '../src/Accounting.gen'
import {
  _make as make,
  _compute as compute,
  _serialize as serialize,
  _deserialize as deserialize,
  _productFromGeneratedStructure as productFromGeneratedStructure,
  _productToGeneratedStructure as productToGeneratedStructure,
} from '../src/Accounting'

describe('Converting private methods', () => {
  const cartGenerated = genMake({
    products: [
      productinputs__quantity_0_unit_price_20_1281_tax_20,
      productinputs__bulk_gram_3_quantity_25_unit_price_5_69_tax_20,
    ],
    discounts: [],
    decimalPrecision: 3,
    currency: 'Eur',
    taxesFree: false,
    standardTaxRate: 20,
  })

  describe('productFromGeneratedStructure', () => {
    const productGenerated = cartGenerated.products[0]
    const bulkProductGenerated = cartGenerated.products[1]

    test('if given a generated Product it converts it', () => {
      const productConverted = productFromGeneratedStructure(productGenerated)

      expect(productConverted?.id).toBeTruthy()
      expect(productConverted).not.toHaveProperty('tag')
      expect(productConverted.capacityPrecision).toBeNull()
      expect(productConverted).toMatchSnapshot()

      const bulkProductConverted =
        productFromGeneratedStructure(bulkProductGenerated)

      expect(bulkProductConverted?.id).toBeTruthy()
      expect(bulkProductConverted).not.toHaveProperty('tag')
      expect(bulkProductConverted).toHaveProperty('capacityPrecision')
      expect(bulkProductConverted).toMatchSnapshot()
    })
  })

  describe('productToGeneratedStructure', () => {
    const productConverted = productFromGeneratedStructure(
      cartGenerated.products[0],
    )
    const bulkProductConverted = productFromGeneratedStructure(
      cartGenerated.products[1],
    )

    test('if given a converted Product it converts it to its origin structure', () => {
      const productGenerated = productToGeneratedStructure(productConverted)

      expect(productGenerated.TAG).toBe('Unit')
      expect(productGenerated).toMatchSnapshot()

      const bulkProductGenerated =
        productToGeneratedStructure(bulkProductConverted)

      expect(bulkProductGenerated.TAG).toBe('Bulk')
      expect(bulkProductGenerated).toMatchSnapshot()
    })
  })

  describe('backward compatibility with legacy ReScript (< v11) TS generated variant format', () => {
    test('handles legacy Unit product format (tag/value)', () => {
      const legacyUnitProduct = {
        tag: 'Unit',
        value: {
          id: 'test-unit-id',
          identifier: null,
          stockKeepingUnit: null,
          name: 'Test Unit Product',
          description: 'A test unit product',
          unitPrice: 10.5,
          capacityUnit: null,
          stock: 100,
          packaging: null,
          quantity: 2,
          expectedQuantity: 2,
          expectedQuantityWarning: [],
          fees: [],
          discounts: [],
          availablesFeeKinds: null,
          totalPrice: null,
          totalLocalDiscounts: null,
          unitFee: null,
          totalFees: null,
          totalAmountExcludingGlobalDiscounts: null,
          totalGlobalDiscounts: null,
          totalDiscounts: null,
          totalTaxesExcludingGlobalDiscount: null,
          totalTaxes: null,
          taxes: null,
          totalAmountExcludingTaxes: null,
          totalAmountIncludingTaxes: null,
          unitCost: null,
          formattedStock: null,
          formattedQuantity: null,
          formattedExpectedQuantity: null,
          formattedUnitPrice: null,
          formattedTotalPrice: null,
          formattedUnitFee: null,
          formattedTotalFees: null,
          formattedTotalLocalDiscounts: null,
          formattedTotalDiscounts: null,
          formattedTotalAmountExcludingGlobalDiscounts: null,
          formattedTotalAmountExcludingTaxes: null,
          formattedTotalAmountIncludingTaxes: null,
        },
      }

      const convertedProduct = productFromGeneratedStructure(
        legacyUnitProduct as any,
      )

      expect(convertedProduct.bulk).toBe(false)
      expect(convertedProduct.capacityPrecision).toBe(null)
      expect(convertedProduct.name).toBe('Test Unit Product')
      expect(convertedProduct.quantity).toBe(2)
    })

    test('handles legacy Bulk product variant format (`value` with array)', () => {
      const legacyBulkProduct = {
        tag: 'Bulk',
        value: [
          {
            id: 'test-bulk-id',
            identifier: null,
            stockKeepingUnit: null,
            name: 'Test Bulk Product',
            description: 'A test bulk product',
            unitPrice: 5.25,
            capacityUnit: 'kg',
            stock: { __type: 'Big', value: '50.5' },
            packaging: null,
            quantity: { __type: 'Big', value: '2.5' },
            expectedQuantity: { __type: 'Big', value: '2.5' },
            expectedQuantityWarning: [],
            fees: [],
            discounts: [],
            availablesFeeKinds: null,
            totalPrice: null,
            totalLocalDiscounts: null,
            unitFee: null,
            totalFees: null,
            totalAmountExcludingGlobalDiscounts: null,
            totalGlobalDiscounts: null,
            totalDiscounts: null,
            totalTaxesExcludingGlobalDiscount: null,
            totalTaxes: null,
            taxes: null,
            totalAmountExcludingTaxes: null,
            totalAmountIncludingTaxes: null,
            unitCost: null,
            formattedStock: null,
            formattedQuantity: null,
            formattedExpectedQuantity: null,
            formattedUnitPrice: null,
            formattedTotalPrice: null,
            formattedUnitFee: null,
            formattedTotalFees: null,
            formattedTotalLocalDiscounts: null,
            formattedTotalDiscounts: null,
            formattedTotalAmountExcludingGlobalDiscounts: null,
            formattedTotalAmountExcludingTaxes: null,
            formattedTotalAmountIncludingTaxes: null,
          },
          3, // precision
        ],
      }

      const convertedProduct = productFromGeneratedStructure(
        legacyBulkProduct as any,
      )

      expect(convertedProduct.bulk).toBe(true)
      expect(convertedProduct.capacityPrecision).toBe(3)
      expect(convertedProduct.name).toBe('Test Bulk Product')
      expect(convertedProduct.capacityUnit).toBe('kg')
    })
  })
})

describe('API layer exposed methods', () => {
  const cartConvertedUncomputed = make({
    products: [
      productinputs__quantity_0_unit_price_20_1281_tax_20,
      productinputs__bulk_gram_3_quantity_25_unit_price_5_69_tax_20,
    ],
    discounts: [],
    decimalPrecision: 3,
    currency: 'Eur',
    taxesFree: false,
    standardTaxRate: 20,
  })

  describe('compute', () => {
    test('if the cart products are correctly computed without converted products', () => {
      const cartConverted = compute(cartConvertedUncomputed)
      const productConverted = compute(cartConverted).products[0]
      const bulkProductConverted = compute(cartConverted).products[1]

      expect(productConverted).not.toHaveProperty('tag')
      expect(productConverted.capacityPrecision).toBeNull

      expect(bulkProductConverted).not.toHaveProperty('tag')
      expect(bulkProductConverted.capacityPrecision).toBeTruthy

      expect(cartConverted.decimalPrecision).toBe(3)
      expect(cartConverted.taxesFree).toBe(false)
      expect(cartConverted).toMatchSnapshot()
    })
  })

  describe('serialize', () => {
    test('if the cart has been correctly stringified with converted products', () => {
      const cartConverted = compute(cartConvertedUncomputed)
      const cartConvertedSerialized = serialize(cartConverted)

      expect(cartConvertedSerialized.includes('"products":[')).toBe(true)
      expect(cartConvertedSerialized.includes('"discounts":[')).toBe(true)
      expect(cartConvertedSerialized.includes('"fees":[')).toBe(true)
      expect(cartConvertedSerialized).toMatchSnapshot()
    })
  })

  describe('deserialize', () => {
    test('if the cart has been correctly stringified with converted products', () => {
      const cartSerialized = serialize(compute(cartConvertedUncomputed))
      const cartDeserialized = deserialize(cartSerialized)

      const productConverted = compute(cartConvertedUncomputed).products[0]
      const bulkProductConverted = compute(cartConvertedUncomputed).products[1]

      expect(productConverted).not.toHaveProperty('tag')
      expect(productConverted.capacityPrecision).toBeNull

      expect(bulkProductConverted).not.toHaveProperty('tag')
      expect(bulkProductConverted.capacityPrecision).toBeTruthy

      expect(cartDeserialized).toMatchSnapshot()
    })
  })
})
